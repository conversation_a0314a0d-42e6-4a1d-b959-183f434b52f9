import { useState } from 'react';
import { RefreshCw, PauseCircle, PlayCircle, CheckCircle, XCircle, Download, Eye, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { TableRow, TableCell } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { hotspotApiEndpoint } from '@/config';
import { toast } from "sonner";
import { SubgroupVisualization } from './subgroup-visualization';

export type JobStatus = 'scheduled' | 'running' | 'completed' | 'failed';
export interface Subgroup {
  description: string;
  target: string;
  pattern_length: number;
  quality: number;
  urr_rate: number;
  impact_radius_relative: number;
  tp: number;
  fp: number;
  TP: number;
  FP: number;
}

export interface JobDetails {
  id: string;
  name: string;
  status: JobStatus;
  progress: number;
  createdAt: Date;
  dimensions: string[];
  targetMetric: {
    column: string;
    value: string;
  };
  maxFeatures: number;
  fileName: string;
  results?: any;
  graphUrl?: string;
}

interface JobHistoryItemProps {
  job: JobDetails;
}

export function JobHistoryItem({ job }: JobHistoryItemProps) {
  const [currentJob, setCurrentJob] = useState<JobDetails>(job);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isGraphDialogOpen, setIsGraphDialogOpen] = useState(false);



  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500';
      case 'running':
        return 'bg-blue-500';
      case 'pending':
        return 'bg-yellow-500';
      case 'failed':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'running':
        return <PlayCircle className="h-4 w-4" />;
      case 'pending':
        return <PauseCircle className="h-4 w-4" />;
      case 'failed':
        return <XCircle className="h-4 w-4" />;
      default:
        return null;
    }
  };

  const refreshJobStatus = async () => {
    if (isRefreshing) return;

    setIsRefreshing(true);
    try {
      const response = await fetch(`${hotspotApiEndpoint}/analysis-jobs/status?jobId=${currentJob.id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to refresh job status: ${response.statusText}`);
      }

      const data = await response.json();

      const updatedJob: JobDetails = {
        ...currentJob,
        status: data.status.toLowerCase(),
        progress: data.status === 'COMPLETED' ? 100 : data.status === 'RUNNING' ? 65 : data.status === 'PENDING' ? 10 : 0,
        results: data.results || null,
        graphUrl: data.graphUrl || null
      };

      setCurrentJob(updatedJob);

      if (data.status === 'COMPLETED' && !currentJob.results) {
        toast.success("Analysis completed", {
          description: "Your analysis results are now available"
        });
      }
    } catch (error) {
      console.error('Error refreshing job status:', error);
      toast.error("Failed to refresh job status", {
        description: error instanceof Error ? error.message : "An unexpected error occurred"
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  const downloadResults = () => {
    if (!currentJob.results) {
      toast.error("No results available", {
        description: "This job has not completed or has no results"
      });
      return;
    }

    try {
      const resultsBlob = new Blob([JSON.stringify(currentJob.results, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(resultsBlob);

      const a = document.createElement('a');
      a.href = url;
      a.download = `analysis-results-${currentJob.id}.json`;
      document.body.appendChild(a);
      a.click();

      URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error downloading results:', error);
      toast.error("Failed to download results", {
        description: "An error occurred while preparing the download"
      });
    }
  };
  const viewResults = async () => {
    console.log('viewing results');

    if (!currentJob.results) {
      toast.error("No results available", {
        description: "This job has not completed or has no results"
      });
      return;
    }

    try {
      // First try to use existing graphUrl
      if (currentJob.graphUrl) {
        // Open the dialog instead of a new tab
        setIsGraphDialogOpen(true);
        return;
      }

      // If no graphUrl, fetch it
      const toastId = toast.loading("Loading graph...");
      const response = await fetch(`${hotspotApiEndpoint}/analysis-jobs/results?jobId=${currentJob.id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        toast.error("Failed to load graph", {
          id: toastId,
          description: `Error: ${response.statusText}`
        });
        throw new Error(`Failed to get graph URL: ${response.statusText}`);
      }

      const data = await response.json();
      if (data.graphUrl) {
        // Update the current job with the new graphUrl
        setCurrentJob(prev => ({
          ...prev,
          graphUrl: data.graphUrl
        }));
        // Open the dialog instead of a new tab
        toast.success("Graph loaded successfully", {
          id: toastId
        });
        setIsGraphDialogOpen(true);
      } else {
        toast.error("Graph not available", {
          id: toastId,
          description: "The graph file could not be accessed"
        });
      }
    } catch (error) {
      console.error('Error viewing results:', error);
      toast.error("Failed to view results", {
        description: error instanceof Error ? error.message : "An unexpected error occurred"
      });
    }
  };
  return (
    <>
      <TableRow className="hover:bg-muted/30">
        {/* Name */}
        <TableCell className="py-3">
          <div className="font-medium">{currentJob.name}</div>
        </TableCell>

        {/* Status */}
        <TableCell className="py-3">
          <div className="flex items-center gap-2">
            <Badge
              variant="outline"
              className={`${getStatusColor(currentJob.status)} text-white`}
            >
              <span className="flex items-center gap-1">
                {getStatusIcon(currentJob.status)}
                {currentJob.status.charAt(0).toUpperCase() + currentJob.status.slice(1)}
              </span>
            </Badge>
            {currentJob.status === 'running' && (
              <Progress value={currentJob.progress} className="h-2 w-16" />
            )}
          </div>
        </TableCell>
        {/* Created Date */}
        <TableCell className="py-3">
          <div className="text-sm">{formatDate(currentJob.createdAt)}</div>
        </TableCell>
        {/* File Name */}
        <TableCell className="py-3">
          <div className="text-sm">{currentJob.fileName.split('/').pop()}</div>
        </TableCell>
        {/* Target Metric */}
        <TableCell className="py-3">
          <div className="text-sm">
            {currentJob.targetMetric.column}: {currentJob.targetMetric.value}
          </div>
        </TableCell>
        {/* Dimensions */}
        <TableCell className="py-3">
          <div className="text-sm truncate max-w-[200px]" title={currentJob.dimensions.join(', ')}>
            {currentJob.dimensions.join(', ') || 'None'}
          </div>
        </TableCell>
        {/* Max Features */}
        <TableCell className="py-3">
          <div className="text-sm">{currentJob.maxFeatures}</div>
        </TableCell>
        {/* Actions */}
        <TableCell className="py-3">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={refreshJobStatus}
              disabled={isRefreshing}
              title="Refresh"
            >
              <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              <span className="sr-only">Refresh</span>
            </Button>
            <Dialog>
              <DialogTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  title="Details"
                >
                  <Info className="h-4 w-4" />
                  <span className="sr-only">Details</span>
                </Button>
              </DialogTrigger>
              <DialogContent className="[&>button]:hidden">
                <DialogHeader>
                  <DialogTitle>Job Details - {currentJob.name}</DialogTitle>
                </DialogHeader>
                <div className="text-sm space-y-4">
                  <div>
                    <div className="font-medium">Status:</div>
                    <div className="text-muted-foreground">{currentJob.status}</div>
                  </div>
                  <div>
                    <div className="font-medium">Created:</div>
                    <div className="text-muted-foreground">{formatDate(currentJob.createdAt)}</div>
                  </div>
                  <div>
                    <div className="font-medium">File:</div>
                    <div className="text-muted-foreground">{currentJob.fileName}</div>
                  </div>
                  <div>
                    <div className="font-medium">Target:</div>
                    <div className="text-muted-foreground">
                      {currentJob.targetMetric.column}: {currentJob.targetMetric.value}
                    </div>
                  </div>
                  <div>
                    <div className="font-medium">Dimensions:</div>
                    <div className="text-muted-foreground">
                      {currentJob.dimensions.join(', ') || 'None'}
                    </div>
                  </div>
                  <div>
                    <div className="font-medium">Max Features:</div>
                    <div className="text-muted-foreground">{currentJob.maxFeatures}</div>
                  </div>
                  {currentJob.results && (
                    <div>
                      <div className="font-medium">Results:</div>
                      <div className="text-muted-foreground">
                        Found {currentJob.results.totalSubgroupsFound || 0} subgroups
                      </div>
                    </div>
                  )}
                </div>
              </DialogContent>
            </Dialog>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={downloadResults}
              disabled={!currentJob.results}
              title="Download Results"
            >
              <Download className="h-4 w-4" />
              <span className="sr-only">Download Results</span>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={viewResults}
              disabled={!currentJob.results}
              title="View Graph"
            >
              <Eye className="h-4 w-4" />
              <span className="sr-only">View Graph</span>
            </Button>
          </div>
        </TableCell>
      </TableRow>
      {/* Graph Dialog */}
      <Dialog open={isGraphDialogOpen} onOpenChange={setIsGraphDialogOpen}>
        <DialogContent className="!max-w-[90vw] !w-[90vw] !h-[90vh] overflow-auto [&>button]:hidden">
          <DialogHeader>
            <DialogTitle>Subgroup Graph - {currentJob.name}</DialogTitle>
          </DialogHeader>
          {currentJob.graphUrl && currentJob.results && (
            <div className="w-full h-full">
              {/* Interactive Visualization */}
              <div className="w-full mb-6">
                <div className="flex flex-col items-center">
                  <SubgroupVisualization
                    subgroups={currentJob.results.subgroups}
                    totalTP={currentJob.results.subgroups[0]?.TP || 0}
                    totalFP={currentJob.results.subgroups[0]?.FP || 0}
                  />
                  
                  {/* PDF View Option */}
                  <div className="mt-2">
                    <Button
                      onClick={() => currentJob.graphUrl && window.open(currentJob.graphUrl, '_blank')}
                      className="flex items-center gap-2"
                      variant="outline"
                      size="sm"
                    >
                      <Eye className="h-4 w-4" />
                      View PDF Graph in New Tab
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
